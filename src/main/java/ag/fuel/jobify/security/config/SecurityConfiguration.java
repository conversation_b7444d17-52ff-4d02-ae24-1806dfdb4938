package ag.fuel.jobify.security.config;

import ag.fuel.jobify.security.filter.JwtAuthenticationFilter;
import ag.fuel.jobify.security.handler.CustomAccessDeniedHandler;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true, jsr250Enabled = true)
@RequiredArgsConstructor
public class SecurityConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(SecurityConfiguration.class);

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final CustomAccessDeniedHandler customAccessDeniedHandler;

    @Value("${security.jwt.name}")
    private String jwtName;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(csrf -> csrf
                        .ignoringRequestMatchers("/auth/**") // Disable CSRF for API endpoints
                        .ignoringRequestMatchers("/users/**") // Disable CSRF for API endpoints
                        .ignoringRequestMatchers("/token") // Disable CSRF for token endpoint
                        .ignoringRequestMatchers("/admin/**") // Disable CSRF for admin API endpoints
                        .ignoringRequestMatchers("/archive-notification/**", "/read-notification/**") // Disable CSRF for admin API endpoints
                        .ignoringRequestMatchers("/mark-all-notifications-*/**", "/*-notification-modal/**") // Disable CSRF for admin API endpoints
                        .ignoringRequestMatchers("/api/**") // Disable CSRF for admin API endpoints
                )
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                        .maximumSessions(-1) // Allow unlimited concurrent sessions (multiple browsers, Postman, etc.)
                        .maxSessionsPreventsLogin(false) // Allow new login without invalidating old sessions
                )
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(HttpMethod.POST, "/auth/**").permitAll()
                        .requestMatchers(HttpMethod.POST, "/token").permitAll()
                        .requestMatchers("/login").permitAll()
                        .requestMatchers(HttpMethod.GET, "/register").permitAll()
                        .requestMatchers(HttpMethod.POST, "/register").permitAll()
                        .requestMatchers(HttpMethod.GET, "/forgot-password").permitAll()
                        .requestMatchers(HttpMethod.POST, "/forgot-password").permitAll()
                        .requestMatchers(HttpMethod.GET, "/reset-password").permitAll()
                        .requestMatchers(HttpMethod.POST, "/reset-password").permitAll()
                        .requestMatchers(HttpMethod.GET, "/activate-user").permitAll()
                        .requestMatchers(HttpMethod.POST, "/activate-user").permitAll()
                        .requestMatchers("/css/**", "/js/**", "/images/**", "/img/**", "/assets/**").permitAll()
                        .requestMatchers("/error/**", "/errors/**", "/", "/home", "/about", "/changeLanguage").permitAll()
                        .requestMatchers("/v3/**", "/swagger-ui/**").permitAll()
                        // Allow Chrome DevTools and other browser development tool requests
                        .requestMatchers("/.well-known/**").permitAll()
                        .requestMatchers("/favicon.ico").permitAll()
                        .requestMatchers(HttpMethod.GET, "/admin/debug/**").authenticated() // Allow debug endpoint for any authenticated user
                        .requestMatchers(HttpMethod.GET, "/admin/test/**").authenticated() // Allow test endpoints for any authenticated user
                        .requestMatchers("/admin/companies/**").hasRole("ADMIN") // Companies management - admin only
                        .requestMatchers("/admin/users/**").hasRole("ADMIN") // User management - admin only
                        .requestMatchers(HttpMethod.POST, "/admin/**").hasRole("ADMIN")
                        .requestMatchers(HttpMethod.GET, "/users/**").hasRole("ADMIN")
                        // App routes - secured by method-level security with custom expressions
                        .requestMatchers("/app/**").authenticated() // Basic authentication required, detailed checks in controllers
                        .anyRequest().authenticated())
                .formLogin(login -> login
                        .loginPage("/login")
                        .permitAll())
                .logout(logout -> logout
                        .logoutSuccessUrl("/login?logout")
                        .invalidateHttpSession(true)
                        .deleteCookies(jwtName) // JWT cookie name from application.properties
                        .permitAll())
                .exceptionHandling(exceptions -> exceptions
                        .accessDeniedHandler(customAccessDeniedHandler))
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        configuration.setAllowedOrigins(List.of("http://localhost:8080"));
        configuration.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE"));
        configuration.setAllowedHeaders(List.of("Authorization", "Content-Type"));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();

        source.registerCorsConfiguration("/**", configuration);

        return source;
    }
}
