package ag.fuel.jobify.security.service;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.service.AppService;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service for handling app-specific security operations.
 * Provides centralized security logic for app access control.
 */
@Service
@RequiredArgsConstructor
public class AppSecurityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppSecurityService.class);

    private final UserService userService;
    private final AppService appService;
    private final UserAppService userAppService;

    /**
     * Validate if the current user can access a specific app.
     *
     * @param appName the name of the app
     * @return SecurityValidationResult containing validation result and details
     */
    public SecurityValidationResult validateAppAccess(String appName) {
        try {
            // Get current user
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                LOGGER.warn("Access denied: No authenticated user for app: {}", appName);
                return SecurityValidationResult.denied("No authenticated user");
            }

            // Check if user is enabled and not locked
            if (!currentUser.isEnabled()) {
                LOGGER.warn("Access denied: User {} is disabled", currentUser.getEmail());
                return SecurityValidationResult.denied("User account is disabled");
            }

            if (currentUser.isAccountLocked()) {
                LOGGER.warn("Access denied: User {} is locked", currentUser.getEmail());
                return SecurityValidationResult.denied("User account is locked");
            }

            // Get the app
            Optional<App> appOpt = appService.getAppByName(capitalizeFirstLetter(appName));
            if (appOpt.isEmpty()) {
                LOGGER.warn("Access denied: App not found: {}", appName);
                return SecurityValidationResult.denied("Application not found");
            }

            App app = appOpt.get();

            // Check if app is enabled
            if (!app.isEnabled()) {
                LOGGER.warn("Access denied: App {} is disabled", appName);
                return SecurityValidationResult.denied("Application is currently disabled");
            }

            // Check if user has access to the app
            if (!userAppService.hasUserAccessToApp(currentUser, app)) {
                LOGGER.warn("Access denied: User {} does not have access to app {}", 
                    currentUser.getEmail(), appName);
                return SecurityValidationResult.denied("Insufficient permissions for this application");
            }

            // Get user's role for the app
            Optional<ERole> userRoleOpt = userAppService.getUserRoleLevelForApp(currentUser, app);
            ERole userRole = userRoleOpt.orElse(ERole.ROLE_USER);

            LOGGER.debug("Access granted: User {} can access app {} with role {}", 
                currentUser.getEmail(), appName, userRole);

            return SecurityValidationResult.allowed(currentUser, app, userRole);

        } catch (Exception e) {
            LOGGER.error("Error validating app access for {}: {}", appName, e.getMessage(), e);
            return SecurityValidationResult.denied("Internal security error");
        }
    }

    /**
     * Validate if the current user can access a specific app with a minimum role.
     *
     * @param appName the name of the app
     * @param minRole the minimum required role
     * @return SecurityValidationResult containing validation result and details
     */
    public SecurityValidationResult validateAppAccessWithRole(String appName, ERole minRole) {
        SecurityValidationResult baseResult = validateAppAccess(appName);
        
        if (!baseResult.isAllowed()) {
            return baseResult;
        }

        ERole userRole = baseResult.getUserRole();
        if (!hasMinimumRole(userRole, minRole)) {
            LOGGER.warn("Access denied: User {} has role {} but requires {} for app {}", 
                baseResult.getUser().getEmail(), userRole, minRole, appName);
            return SecurityValidationResult.denied("Insufficient role level for this operation");
        }

        return baseResult;
    }

    /**
     * Check if a role meets the minimum requirement.
     *
     * @param userRole the user's role
     * @param requiredRole the minimum required role
     * @return true if the user's role meets the requirement
     */
    private boolean hasMinimumRole(ERole userRole, ERole requiredRole) {
        int userLevel = getRoleLevel(userRole);
        int requiredLevel = getRoleLevel(requiredRole);
        return userLevel >= requiredLevel;
    }

    /**
     * Get the numeric level of a role for comparison.
     *
     * @param role the role to get the level for
     * @return the numeric level (higher = more privileged)
     */
    private int getRoleLevel(ERole role) {
        switch (role) {
            case ROLE_USER:
                return 1;
            case ROLE_MODERATOR:
                return 2;
            case ROLE_ADMIN:
                return 3;
            default:
                return 0;
        }
    }

    /**
     * Utility method to capitalize the first letter of a string.
     */
    private String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    /**
     * Result class for security validation operations.
     */
    public static class SecurityValidationResult {
        private final boolean allowed;
        private final String reason;
        private final User user;
        private final App app;
        private final ERole userRole;

        private SecurityValidationResult(boolean allowed, String reason, User user, App app, ERole userRole) {
            this.allowed = allowed;
            this.reason = reason;
            this.user = user;
            this.app = app;
            this.userRole = userRole;
        }

        public static SecurityValidationResult allowed(User user, App app, ERole userRole) {
            return new SecurityValidationResult(true, "Access granted", user, app, userRole);
        }

        public static SecurityValidationResult denied(String reason) {
            return new SecurityValidationResult(false, reason, null, null, null);
        }

        // Getters
        public boolean isAllowed() { return allowed; }
        public String getReason() { return reason; }
        public User getUser() { return user; }
        public App getApp() { return app; }
        public ERole getUserRole() { return userRole; }
    }
}
