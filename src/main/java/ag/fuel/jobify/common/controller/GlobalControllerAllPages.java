package ag.fuel.jobify.common.controller;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.repository.UserRepository;
import ag.fuel.jobify.user.service.UserService;
import ag.fuel.jobify.notification.service.NotificationService;
import ag.fuel.jobify.reference.service.VersionService;
import ag.fuel.jobify.common.util.Constants;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;

import java.security.Principal;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@ControllerAdvice
@RequiredArgsConstructor
public class GlobalControllerAllPages {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalControllerAllPages.class);

    private final NotificationService notificationService;
    private final UserService userService;
    private final UserAppService userAppService;
    private final VersionService versionService;
    private final UserRepository userRepository;

    @ModelAttribute("currentVersion")
    public String getCurrentVersion(HttpServletRequest request) {

        if (!request.getRequestURI().startsWith(Constants.IMAGES_UPLOAD_FOLDER)) {

            return versionService.getCurrentVersion().getLabel();
        } else
            return null;
    }

    @ModelAttribute
    public void addGlobalAttributes(Principal p, Model model, HttpServletRequest request) {
        /*Get Current User from Context*/
        User currentUser = userService.getCurrentUser();
        if (currentUser != null) {
            model.addAttribute("currentUser", currentUser);
            // Add the user's role to the model
            model.addAttribute("mainRole", userService.getMainRole(currentUser.getEmail()));
            // Add user's available apps to the model for dynamic menu generation
            List<App> userAvailableApps = userAppService.getAvailableAppsForUser(currentUser);
            model.addAttribute("userAvailableApps", userAvailableApps);

            // Detect current app from URL for active menu highlighting
            String currentAppName = extractAppNameFromUrl(request.getRequestURI());
            model.addAttribute("currentAppName", currentAppName);

            LOGGER.debug("Added {} available apps for user: {}, current app: {}",
                userAvailableApps != null ? userAvailableApps.size() : 0,
                currentUser.getEmail(), currentAppName);
        } else {
            LOGGER.debug("No current user found, setting userAvailableApps to null");
            model.addAttribute("userAvailableApps", null);
            model.addAttribute("currentAppName", null);
        }
    }

    @ModelAttribute("userNotifications")
    public String getUserNotifications(Principal p, Model model) {

        /*Get Current User from Context*/
        User currentUser = userService.getCurrentUser();
        if (currentUser != null) {
            // Use navbar-specific method that loads only top 10 notifications for better performance
            notificationService.addAttributesForNavbar(model, currentUser);
        }

        return "fragments/navbar :: navbar";
    }

    /**
     * Extract app name from URL path for active menu highlighting.
     * Supports URLs like: /app/purchasing, /app/purchasing/settings, /app/finance/admin
     *
     * @param requestUri the request URI
     * @return the app name if found, null otherwise
     */
    private String extractAppNameFromUrl(String requestUri) {
        if (requestUri == null || requestUri.isEmpty()) {
            return null;
        }

        // Pattern to match /app/{appName} or /app/{appName}/anything
        Pattern appPattern = Pattern.compile("^/app/([a-zA-Z]+)(?:/.*)?$");
        Matcher matcher = appPattern.matcher(requestUri);

        if (matcher.matches()) {
            String appName = matcher.group(1);
            LOGGER.debug("Extracted app name '{}' from URL: {}", appName, requestUri);
            return appName.toLowerCase();
        }

        LOGGER.debug("No app name found in URL: {}", requestUri);
        return null;
    }
}
