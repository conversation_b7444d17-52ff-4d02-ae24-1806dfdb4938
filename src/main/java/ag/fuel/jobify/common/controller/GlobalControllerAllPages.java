package ag.fuel.jobify.common.controller;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.repository.UserRepository;
import ag.fuel.jobify.user.service.UserService;
import ag.fuel.jobify.notification.service.NotificationService;
import ag.fuel.jobify.reference.service.VersionService;
import ag.fuel.jobify.common.util.Constants;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;

import java.security.Principal;
import java.util.List;

@ControllerAdvice
@RequiredArgsConstructor
public class GlobalControllerAllPages {

    private final NotificationService notificationService;
    private final UserService userService;
    private final UserAppService userAppService;
    private final VersionService versionService;
    private final UserRepository userRepository;

    @ModelAttribute("currentVersion")
    public String getCurrentVersion(HttpServletRequest request) {

        if (!request.getRequestURI().startsWith(Constants.IMAGES_UPLOAD_FOLDER)) {

            return versionService.getCurrentVersion().getLabel();
        } else
            return null;
    }

    @ModelAttribute("userNotifications")
    public String getUserNotifications(Principal p, Model model) {

        /*Get Current User from Context*/
        User currentUser = userService.getCurrentUser();
        if (currentUser != null) {
            model.addAttribute("currentUser", currentUser);
            // Add the user's role to the model
            model.addAttribute("mainRole", userService.getMainRole(currentUser.getEmail()));
            // Add user's available apps to the model for dynamic menu generation
            List<App> userAvailableApps = userAppService.getAvailableAppsForUser(currentUser);
            model.addAttribute("userAvailableApps", userAvailableApps);
            // Use navbar-specific method that loads only top 10 notifications for better performance
            notificationService.addAttributesForNavbar(model, currentUser);
        }

        return "fragments/navbar :: navbar";
    }
}
