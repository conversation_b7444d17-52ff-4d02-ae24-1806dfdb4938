package ag.fuel.jobify.app.controller;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.service.AppService;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.security.service.AppSecurityService;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Optional;

/**
 * Controller for handling app-specific pages and functionality.
 */
@Controller
@RequestMapping("/app")
@RequiredArgsConstructor
@Tag(name = "App Controller", description = "Handles app-specific pages and functionality")
public class AppController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppController.class);

    private final AppService appService;
    private final UserAppService userAppService;
    private final UserService userService;
    private final AppSecurityService appSecurityService;

    /**
     * Display app-specific dashboard/main page
     */
    @GetMapping("/{appName}")
    @PreAuthorize("isAuthenticated() and @appSecurityService.validateAppAccess(#appName).allowed")
    @Operation(summary = "Get app dashboard", description = "Display the main dashboard for a specific app")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "App dashboard loaded successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "App not found")
    })
    public String appDashboard(
            @Parameter(description = "Name of the app to access", example = "purchasing")
            @PathVariable String appName,
            Model model,
            @AuthenticationPrincipal User currentUser) {
        LOGGER.debug("Accessing app dashboard for: {} by user: {}", appName,
            currentUser != null ? currentUser.getEmail() : "unknown");

        // Validate access using security service
        AppSecurityService.SecurityValidationResult validation =
            appSecurityService.validateAppAccess(appName);

        if (!validation.isAllowed()) {
            LOGGER.warn("Access denied for user {} to app {}: {}",
                currentUser != null ? currentUser.getEmail() : "unknown",
                appName, validation.getReason());

            // Return appropriate error page based on the reason
            if (validation.getReason().contains("not found")) {
                return "error/404";
            } else {
                return "error/403";
            }
        }

        App app = validation.getApp();
        User validatedUser = validation.getUser();

        // Get user's role level for this app (from validation result)
        ag.fuel.jobify.auth.entity.ERole userRoleLevel = validation.getUserRole();

        // Add data to model
        model.addAttribute("app", app);
        model.addAttribute("appName", app.getAppName());
        model.addAttribute("appDescription", app.getDescription());
        model.addAttribute("userRoleLevel", userRoleLevel);
        model.addAttribute("currentUser", validatedUser);

        // Get all available apps for the user (for navigation)
        List<App> userAvailableApps = userAppService.getAvailableAppsForUser(validatedUser);
        model.addAttribute("userAvailableApps", userAvailableApps);

        LOGGER.info("User {} successfully accessed {} app with role: {}",
                validatedUser.getEmail(), app.getAppName(), userRoleLevel);

        // Return app-specific template based on app name
        return "app/" + appName.toLowerCase() + "/dashboard";
    }

    /**
     * Display app settings page (requires moderator or admin role)
     */
    @GetMapping("/{appName}/settings")
    @PreAuthorize("isAuthenticated() and @appSecurityService.validateAppAccessWithRole(#appName, T(ag.fuel.jobify.auth.entity.ERole).ROLE_MODERATOR).allowed")
    @Operation(summary = "Get app settings", description = "Display the settings page for a specific app (moderator+ only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "App settings loaded successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "App not found")
    })
    public String appSettings(
            @Parameter(description = "Name of the app", example = "purchasing")
            @PathVariable String appName,
            Model model,
            @AuthenticationPrincipal User currentUser) {

        LOGGER.debug("Accessing app settings for: {} by user: {}", appName,
            currentUser != null ? currentUser.getEmail() : "unknown");

        // Validate access with moderator role requirement
        AppSecurityService.SecurityValidationResult validation =
            appSecurityService.validateAppAccessWithRole(appName, ag.fuel.jobify.auth.entity.ERole.ROLE_MODERATOR);

        if (!validation.isAllowed()) {
            LOGGER.warn("Settings access denied for user {} to app {}: {}",
                currentUser != null ? currentUser.getEmail() : "unknown",
                appName, validation.getReason());
            return "error/403";
        }

        App app = validation.getApp();
        User validatedUser = validation.getUser();

        // Add data to model
        model.addAttribute("app", app);
        model.addAttribute("appName", app.getAppName());
        model.addAttribute("userRoleLevel", validation.getUserRole());
        model.addAttribute("currentUser", validatedUser);

        LOGGER.info("User {} successfully accessed {} app settings with role: {}",
                validatedUser.getEmail(), app.getAppName(), validation.getUserRole());

        return "app/" + appName.toLowerCase() + "/settings";
    }

    /**
     * Display app admin panel (requires admin role)
     */
    @GetMapping("/{appName}/admin")
    @PreAuthorize("isAuthenticated() and @appSecurityService.validateAppAccessWithRole(#appName, T(ag.fuel.jobify.auth.entity.ERole).ROLE_ADMIN).allowed")
    @Operation(summary = "Get app admin panel", description = "Display the admin panel for a specific app (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "App admin panel loaded successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied - insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "App not found")
    })
    public String appAdmin(
            @Parameter(description = "Name of the app", example = "purchasing")
            @PathVariable String appName,
            Model model,
            @AuthenticationPrincipal User currentUser) {

        LOGGER.debug("Accessing app admin for: {} by user: {}", appName,
            currentUser != null ? currentUser.getEmail() : "unknown");

        // Validate access with admin role requirement
        AppSecurityService.SecurityValidationResult validation =
            appSecurityService.validateAppAccessWithRole(appName, ag.fuel.jobify.auth.entity.ERole.ROLE_ADMIN);

        if (!validation.isAllowed()) {
            LOGGER.warn("Admin access denied for user {} to app {}: {}",
                currentUser != null ? currentUser.getEmail() : "unknown",
                appName, validation.getReason());
            return "error/403";
        }

        App app = validation.getApp();
        User validatedUser = validation.getUser();

        // Add data to model
        model.addAttribute("app", app);
        model.addAttribute("appName", app.getAppName());
        model.addAttribute("userRoleLevel", validation.getUserRole());
        model.addAttribute("currentUser", validatedUser);

        LOGGER.info("User {} successfully accessed {} app admin with role: {}",
                validatedUser.getEmail(), app.getAppName(), validation.getUserRole());

        return "app/" + appName.toLowerCase() + "/admin";
    }

    /**
     * Utility method to capitalize first letter of a string
     */
    private String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
}
