package ag.fuel.jobify.app.controller;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.service.AppService;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Optional;

/**
 * Controller for handling app-specific pages and functionality.
 */
@Controller
@RequestMapping("/app")
@RequiredArgsConstructor
@Tag(name = "App Controller", description = "Handles app-specific pages and functionality")
public class AppController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppController.class);

    private final AppService appService;
    private final UserAppService userAppService;
    private final UserService userService;

    /**
     * Display app-specific dashboard/main page
     */
    @GetMapping("/{appName}")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    @Operation(summary = "Get app dashboard", description = "Display the main dashboard for a specific app")
    public String appDashboard(@PathVariable String appName, Model model) {
        LOGGER.debug("Accessing app dashboard for: {}", appName);

        // Get current user
        User currentUser = userService.getCurrentUser();
        if (currentUser == null) {
            LOGGER.warn("No current user found");
            return "redirect:/login";
        }

        // Get the app by name
        Optional<App> appOpt = appService.getAppByName(capitalizeFirstLetter(appName));
        if (appOpt.isEmpty()) {
            LOGGER.warn("App not found: {}", appName);
            return "error/404";
        }

        App app = appOpt.get();

        // Check if user has access to this app
        if (!userAppService.hasUserAccessToApp(currentUser, app)) {
            LOGGER.warn("User {} does not have access to app: {}", currentUser.getEmail(), appName);
            return "error/403";
        }

        // Get user's role level for this app
        Optional<ag.fuel.jobify.auth.entity.ERole> userRoleLevel = userAppService.getUserRoleLevelForApp(currentUser, app);

        // Add data to model
        model.addAttribute("app", app);
        model.addAttribute("appName", app.getAppName());
        model.addAttribute("appDescription", app.getDescription());
        model.addAttribute("userRoleLevel", userRoleLevel.orElse(null));
        model.addAttribute("currentUser", currentUser);

        // Get all available apps for the user (for navigation)
        List<App> userAvailableApps = userAppService.getAvailableAppsForUser(currentUser);
        model.addAttribute("userAvailableApps", userAvailableApps);

        LOGGER.debug("User {} accessing {} app with role: {}", 
                currentUser.getEmail(), app.getAppName(), userRoleLevel.orElse(null));

        // Return app-specific template based on app name
        return "app/" + appName.toLowerCase() + "/dashboard";
    }

    /**
     * Utility method to capitalize first letter of a string
     */
    private String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
}
