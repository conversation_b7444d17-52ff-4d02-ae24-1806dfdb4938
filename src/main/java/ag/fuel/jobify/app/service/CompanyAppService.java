package ag.fuel.jobify.app.service;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.entity.CompanyApp;
import ag.fuel.jobify.app.repository.CompanyAppRepository;
import ag.fuel.jobify.company.entity.Company;
import ag.fuel.jobify.company.repository.CompanyRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing CompanyApp entities.
 */
@Service
@RequiredArgsConstructor
public class CompanyAppService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompanyAppService.class);

    private final CompanyAppRepository companyAppRepository;
    private final AppService appService;
    private final CompanyRepository companyRepository;

    /**
     * Get all CompanyApp entities.
     *
     * @return a list of all CompanyApp entities
     */
    @Transactional(readOnly = true)
    public List<CompanyApp> getAllCompanyApps() {
        return companyAppRepository.findAll();
    }

    /**
     * Get a CompanyApp entity by its ID.
     *
     * @param id the CompanyApp ID
     * @return an Optional containing the CompanyApp if found, or empty if not found
     */
    @Transactional(readOnly = true)
    public Optional<CompanyApp> getCompanyAppById(Long id) {
        return companyAppRepository.findById(id);
    }

    /**
     * Get all CompanyApp entities for a given company.
     *
     * @param company the company
     * @return a list of CompanyApp entities
     */
    @Transactional(readOnly = true)
    public List<CompanyApp> getCompanyAppsByCompany(Company company) {
        return companyAppRepository.findByCompany(company);
    }

    /**
     * Get all CompanyApp entities for a given company ID.
     *
     * @param companyId the company ID
     * @return a list of CompanyApp entities
     */
    @Transactional(readOnly = true)
    public List<CompanyApp> getCompanyAppsByCompanyId(Long companyId) {
        return companyAppRepository.findByCompanyId(companyId);
    }

    /**
     * Get all apps for a given company.
     *
     * @param company the company
     * @return a list of apps
     */
    @Transactional(readOnly = true)
    public List<App> getAppsByCompany(Company company) {
        return companyAppRepository.findByCompany(company)
                .stream()
                .map(CompanyApp::getApp)
                .filter(app -> app != null && app.getAppName() != null && !app.getAppName().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * Get all apps for a given company ID.
     *
     * @param companyId the company ID
     * @return a list of apps
     */
    @Transactional(readOnly = true)
    public List<App> getAppsByCompanyId(Long companyId) {
        return companyAppRepository.findByCompanyId(companyId)
                .stream()
                .map(CompanyApp::getApp)
                .filter(app -> app != null && app.getAppName() != null && !app.getAppName().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * Get a CompanyApp entity for a given company and app.
     *
     * @param company the company
     * @param app the app
     * @return an Optional containing the CompanyApp if found, or empty if not found
     */
    @Transactional(readOnly = true)
    public Optional<CompanyApp> getCompanyAppByCompanyAndApp(Company company, App app) {
        return companyAppRepository.findByCompanyAndApp(company, app);
    }

    /**
     * Check if a company has access to a specific app.
     *
     * @param company the company
     * @param app the app
     * @return true if the company has access to the app, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean hasCompanyAccessToApp(Company company, App app) {
        Optional<CompanyApp> companyApp = companyAppRepository.findByCompanyAndApp(company, app);
        return companyApp.isPresent() && companyApp.get().isEnabled();
    }

    /**
     * Check if a company has access to a specific app by IDs.
     *
     * @param companyId the company ID
     * @param appId the app ID
     * @return true if the company has access to the app, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean hasCompanyAccessToApp(Long companyId, Long appId) {
        Optional<CompanyApp> companyApp = companyAppRepository.findByCompanyIdAndAppId(companyId, appId);
        return companyApp.isPresent() && companyApp.get().isEnabled();
    }

    /**
     * Create a new CompanyApp entity.
     *
     * @param companyApp the CompanyApp to create
     * @return the created CompanyApp
     */
    @Transactional
    public CompanyApp createCompanyApp(CompanyApp companyApp) {
        return companyAppRepository.save(companyApp);
    }

    /**
     * Create a new CompanyApp entity from company and app.
     *
     * @param company the company
     * @param app the app
     * @return the created CompanyApp
     */
    @Transactional
    public CompanyApp createCompanyApp(Company company, App app) {
        CompanyApp companyApp = new CompanyApp(company, app);
        return companyAppRepository.save(companyApp);
    }

    /**
     * Create a new CompanyApp entity from company ID and app ID.
     *
     * @param companyId the company ID
     * @param appId the app ID
     * @return the created CompanyApp
     */
    @Transactional
    public CompanyApp createCompanyApp(Long companyId, Long appId) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new IllegalArgumentException("Company not found with ID: " + companyId));
        App app = appService.getAppById(appId)
                .orElseThrow(() -> new IllegalArgumentException("App not found with ID: " + appId));

        CompanyApp companyApp = new CompanyApp(company, app);
        return companyAppRepository.save(companyApp);
    }

    /**
     * Update an existing CompanyApp entity.
     *
     * @param companyApp the CompanyApp to update
     * @return the updated CompanyApp
     */
    @Transactional
    public CompanyApp updateCompanyApp(CompanyApp companyApp) {
        return companyAppRepository.save(companyApp);
    }

    /**
     * Enable or disable a CompanyApp entity.
     *
     * @param id the CompanyApp ID
     * @param enabled the enabled status
     * @return the updated CompanyApp
     */
    @Transactional
    public CompanyApp setCompanyAppEnabled(Long id, boolean enabled) {
        CompanyApp companyApp = companyAppRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("CompanyApp not found with ID: " + id));
        companyApp.setEnabled(enabled);
        return companyAppRepository.save(companyApp);
    }

    /**
     * Delete a CompanyApp entity by its ID.
     *
     * @param id the CompanyApp ID
     */
    @Transactional
    public void deleteCompanyApp(Long id) {
        companyAppRepository.deleteById(id);
    }

    /**
     * Delete all CompanyApp entities for a given company.
     *
     * @param company the company
     */
    @Transactional
    public void deleteCompanyAppsByCompany(Company company) {
        companyAppRepository.deleteByCompany(company);
    }

    /**
     * Delete a CompanyApp entity for a given company and app.
     *
     * @param company the company
     * @param app the app
     */
    @Transactional
    public void deleteCompanyAppByCompanyAndApp(Company company, App app) {
        companyAppRepository.deleteByCompanyAndApp(company, app);
    }
}
