page.title.user.details=User Details


#Page Contents
page.login.bad.credentials.message=Bad Credentials!
page.user.content.password.changed.message=Password Changed!
page.content.generic.error=An error occurred!
page.content.not.defined=--n/d--

#Buttons
page.user.button.change.password=Change Password



#Messages
modal.title.save.success=Success
modal.title.save.error=Error
modal.text.save.success=Changes have been saved!
modal.text.save.error=Changes not saved!
page.landing.menu.home=Home
page.landing.menu.features=Features
page.landing.menu.team=Team
page.landing.menu.faq=FAQ
page.landing.menu.contact=Contact
page.login.title=Welcome to Jobify! 👋
general.language.lang_1=Portuguese
general.language.lang_2=English
page.landing.menu.login=Login
page.login.subtitle=Please enter your credentials
page.login.email=E-Mail
page.login.password=Password
page.login.rememberme=Remember Me
page.login.forgotpassword=Forgot your password?
page.login.requestaccess=Request here
page.login.withoutaccess=Don't have access yet?
page.user.security.change.password.req.01=The new password cannot contain spaces
page.user.security.change.password.req.02=The new password must have at least one uppercase character
page.user.security.change.password.req.03=New password must have at least one lowercase character
page.user.security.change.password.req.04=The new password must contain at least one number from 0 to 9
page.user.security.change.password.req.05=New password must contain at least one special character
page.user.security.change.password.req.06=The new password must be at least 8 characters long
page.user.security.change.password.req.07=The new password must not contain sequences of 4 or more numbers
page.user.security.change.password.req.08=The new password cannot be the same as the current one
page.user.security.change.password.req.09=The password entered does not match!
modal.text.save.current.password.error=Current password not entered correctly
modal.text.save.password.matching=Password is not matching!
page.user.my.account=My Account
page.user.security=Security
page.login.captcha.verify=Please verify that you are not a robot
page.login.token.expired=Your session has expired. Please log in again.
page.navbar.profile=My Profile
page.navbar.logout=Logout
page.navbar.my.account=
page.navbar.faqs=FAQs

# Password Reset Messages
message.resetPassword.emailNotFound=No account found with that email address.
message.resetPassword.linkSent=If an account exists for {0}, a password reset link has been sent.
message.resetPassword.tokenInvalidOrExpired=Invalid or expired password reset token.
message.resetPassword.formError=Please correct the errors in the form.
message.resetPassword.passwordsDontMatch=Passwords do not match.
message.resetPassword.success=Your password has been successfully reset. Please login.

# Email specific messages
email.resetPassword.subject=Jobify - Password Reset Request
message.resetPassword.emailSendError=Could not send the password reset email. Please try again later or contact support.

# Menu items
menu.companies=Companies
menu.users=Users
menu.admin=Administrator

# Company management
company.title=Companies Management
company.list.title=Companies List
company.form.title.create=Create Company
company.form.title.edit=Edit Company
company.form.description=Form for adding or editing company information
company.field.id=ID
company.field.companyName=Company Name
company.field.address=Address
company.field.country=Country
company.field.country.select=Select Country
company.field.country.placeholder=Search and select country...
company.field.country.no.results=No countries found
company.field.country.searching=Searching...
company.field.taxNumber=TAX Number
company.field.taxNumber.readonly=TAX number cannot be changed
company.field.domainName=Domain Name
company.field.status=Status
company.field.lock=Lock Status
company.field.createdAt=Created At
company.field.updatedAt=Updated At
company.button.create=Create Company
company.button.edit=Edit
company.button.delete=Delete
company.button.save=Save
company.button.cancel=Cancel
company.button.clear.filters=Clear Filters
company.button.refresh=Refresh
company.button.lock=Lock
company.button.unlock=Unlock
company.button.enable=Enable
company.button.disable=Disable
company.button.view.users=View Users
company.filter.companyName=Filter by Company Name
company.filter.country=Filter by Country
company.filter.taxNumber=Filter by TAX Number
company.filter.country.all=All Countries
company.filter.status.all=All Status
company.filter.lock.all=All Locks
company.validation.error=Please correct the validation errors
company.create.success=Company created successfully
company.create.error=Error creating company
company.update.success=Company updated successfully
company.update.error=Error updating company
company.delete.success=Company deleted successfully
company.delete.error=Error deleting company
company.lock.success=Company locked successfully
company.lock.error=Error locking company
company.unlock.success=Company unlocked successfully
company.unlock.error=Error unlocking company
company.enable.success=Company enabled successfully
company.enable.error=Error enabling company
company.disable.success=Company disabled successfully
company.disable.error=Error disabling company
company.delete.confirm=Are you sure you want to delete this company?
company.delete.confirm.title=Are you sure?
company.delete.confirm.text=Do you want to delete the company: {0}?
company.delete.confirm.yes=Yes, delete it!
company.delete.confirm.cancel=Cancel
company.lock.confirm=Are you sure you want to lock this company?
company.unlock.confirm=Are you sure you want to unlock this company?
company.enable.confirm=Are you sure you want to enable this company?
company.disable.confirm=Are you sure you want to disable this company?
company.not.found=Company not found
company.tax.number.exists=A company with TAX number {0} already exists
company.tax.number.cannot.change=TAX number cannot be changed

# Company UI texts
company.loading=Loading...
company.list.empty=No companies found
company.field.taxNumber.help=This must be unique for each company
company.field.domainName.help=Optional - company website domain
company.pagination.label=Company pagination

# Company Status
company.status.enabled=Enabled
company.status.disabled=Disabled
company.status.locked=Locked
company.status.unlocked=Unlocked

# Company Management
company.management.title=Company Management
company.management.subtitle=Manage companies and their information

# Company Statistics
company.stats.total=Total Companies
company.stats.total.description=All registered companies
company.stats.enabled=Enabled Companies
company.stats.enabled.description=Active companies
company.stats.disabled=Disabled Companies
company.stats.disabled.description=Inactive companies
company.stats.locked=Locked Companies
company.stats.locked.description=Temporarily locked companies

# Company Users Management
company.users.title=Company Users
company.users.description=View and manage users for this company
company.users.search.placeholder=Search users...
company.users.clear.filter=Clear filter
company.users.no.users.found=No users found for this company
company.users.selected.count={0} users selected
company.users.selected.count.suffix=users selected
company.users.remove.from.company=Remove from Company
company.users.remove.confirm=Are you sure you want to remove the selected users from this company?
company.users.add.title=Add Users to Company
company.users.add.selected=Add Selected
company.users.available.search.placeholder=Search available users...
company.users.available.no.users.found=No available users found
company.users.pagination.label=User pagination

# Table headers
table.header.actions=Actions

# Table pagination
table.pagination.showing.from=Showing from
table.pagination.to=to
table.pagination.of.total=of total
table.pagination.entries=entries
table.pagination.users=users

# Common UI elements
common.search.and.select=Search and select...
common.loading=Loading...
common.cancel=Cancel
common.close=Close

# App Names
app.name.purchasing=Purchasing
app.name.finance=Finance
app.name.budgets=Budgets

# Validation Messages
validation.password.notBlank=Password cannot be empty.
validation.password.size=Password must be at least 8 characters long.
validation.password.pattern=Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@#$!%*?&.;:<>).
validation.confirmPassword.notBlank=Confirm password cannot be empty.
user.validation.fullName.required=Please enter your name.
user.validation.company.required=Please enter the company name you are working for.
user.validation.email.required=Registration email is required.

# Rate Limiting
error.tooManyRequests=Too many requests. Please try again later.
page.user.security.password.policies.01=Minimum 8 characters - the more the better
page.user.security.password.policies.02=At least one lowercase letter
page.user.security.password.policies.03=At least one number (0-9)
page.user.security.password.policies.04=At least one uppercase letter
page.user.security.password.policies.05=At least one special character other than space (e.g., @#$!%*?&.;:<>)
page.user.security.password.policies.06=Must not contain sequences of 4 or more numbers
page.user.reset.password.title=Set New Password
page.user.reset.password.subtitle=Please enter your new password below
page.user.reset.password.input.password=New Password
page.user.reset.password.input.confirmation=Confirm New Password
page.user.reset.password.button.reset=Reset Password
page.user.reset.password.button.back=Back to Login
page.user.reset.password.input.password.hint=Password must meet the requirements below
page.user.reset.password.requirements=Password Requirements:
page.user.reset.password.success.title=Password Reset Successful!
page.user.reset.password.success.submessage=You can now log in with your new password.
page.user.forgot.password.title=Forgot Password? 🔒
page.user.forgot.password.subtitle=Enter your email and we'll send you instructions to reset your password
page.user.forgot.password.input.email=Email
page.user.forgot.password.button.send=Send Reset Link
page.user.forgot.password.button.back=Back to login
page.user.forgot.password.input.email.placeholder=Enter your email
page.user.forgot.password.success.title=Email Sent Successfully!
page.user.forgot.password.success.message=Please check your email inbox and spam folder for the password reset link.
page.user.reset.password.error.submessage=Please try again or request a new password reset link.
page.user.reset.password.error.title=Password Reset Failed ❌
email.password.reset.title=Password Reset Request
email.password.reset.p.01=Hello
email.password.reset.p.02=You (or someone on your behalf) recently requested to reset your password for your Jobify account. If this was you, please click the button below to set a new password:
email.password.reset.p.03=If you did not request a password reset, please ignore this email. This link is valid for 60 minutes.
email.password.reset.footer.01=Thank you,
email.password.reset.footer.02=The Jobify Team
email.password.reset.button=Reset Your Password

# User Activation Email
email.user.activation.title=Complete Your User Registration
email.user.activation.p.01=Hello
email.user.activation.p.02=Your user account has been created in Jobify. To complete the registration process and set your password, please click the button below.
email.user.activation.p.03=This link is only valid for the next 24 hours. If you did not expect this email, please ignore it.
email.user.activation.footer.01=Thank you,
email.user.activation.footer.02=The Jobify Team
email.user.activation.button=Complete Registration

# User Activation Messages
message.userActivation.tokenInvalidOrExpired=The activation link is invalid or has expired. Please contact your administrator.
message.userActivation.success=Your account has been successfully activated! You can now log in with your email and password.

# User Activation Page
page.user.activate.title=Activate User Account
page.user.activate.success.title=Account Activation Successful!
page.user.activate.success.message=Your account has been successfully activated!
page.user.activate.success.submessage=You can now log in with your email and password.
page.user.activate.error.title=Account Activation Failed ❌
page.user.activate.error.submessage=Please try again or contact support for assistance.
page.user.activate.form.title=Complete Your Registration
page.user.activate.form.subtitle=Set your password to activate your account
page.user.activate.form.password=Password
page.user.activate.form.confirmPassword=Confirm Password
page.user.activate.form.button=Activate Account
page.user.activate.form.back=Back to Login

# Email verification messages
email.verification.subject=Jobify - Account Verification
email.verification.title=Account Verification
email.verification.greeting=Dear
email.verification.p.01=Thank you for registering with Jobify. Please click the button below to verify your account:
email.verification.p.02=This link will expire in 24 hours.
email.verification.p.03=If you did not register for an account, please ignore this email.
email.verification.button=Verify Account
email.verification.footer.01=Thank you,
email.verification.footer.02=The Jobify Team

# Notifications Modal
modal.notifications.title=All Notifications
modal.notifications.subtitle=Manage your notifications
modal.notifications.status.new=New
modal.notifications.status.read=Read
modal.notifications.status.archived=Archived
modal.notifications.empty.message=No notifications found
modal.notifications.close=Close
modal.notifications.mark.all.read=Mark all as read
modal.notifications.archive.all=Archive all
modal.notifications.pagination.showing=Showing
modal.notifications.pagination.to=to
modal.notifications.pagination.of=of
modal.notifications.pagination.notifications=notifications
modal.notifications.pagination.page=Page
modal.notifications.pagination.previous=Previous
modal.notifications.pagination.next=Next
modal.notifications.pagination.first=First page
modal.notifications.pagination.last=Last page
modal.notifications.list.title=My Notifications List
dropdown.notifications.title=Notifications
dropdown.notifications.latest.indicator=(Latest 10)
dropdown.notifications.new.singular=New
dropdown.notifications.new.plural=New

#Recent Activity Filters
filter.activity.all.events=All events
filter.date.placeholder=DD/MM/YYYY
filter.date.title=Filter by date (ex: 25/12/2024, 25/12, 12/2024)
filter.clear.title=Clear filters

#Recent Activity Table
table.header.filters=Filters
table.header.browser=Browser
table.header.ip.address=IP Address
table.header.location=Location
table.header.date.time=Date/Time
table.header.event=Event
table.browser.on=on
table.pagination.showing.from=Showing from
table.pagination.to=to
table.pagination.of.total=of total
table.pagination.entries=entries

# User Management
user.management.title=User Management
user.management.subtitle=Manage system users, roles, and permissions
user.list.title=Users List
user.create.title=Create New User
user.edit.title=Edit User
user.create.success=User created successfully
user.create.error=Error creating user
user.update.success=User updated successfully
user.update.error=Error updating user
user.delete.success=User deleted successfully
user.delete.error=Error deleting user
user.delete.confirm=Are you sure you want to delete this user?
user.deactivate.success=User deactivated successfully
user.deactivate.error=Error deactivating user
user.deactivate.confirm=Are you sure you want to deactivate this user?
user.activate.success=User activated successfully
user.activate.error=Error activating user
user.activate.confirm=Are you sure you want to activate this user?
user.lock.success=User locked successfully
user.lock.error=Error locking user
user.lock.confirm=Are you sure you want to lock this user?
user.unlock.success=User unlocked successfully
user.unlock.error=Error unlocking user
user.unlock.confirm=Are you sure you want to unlock this user?
user.not.found=User not found
user.email.exists=A user with email {0} already exists
user.cannot.delete.self=Cannot delete your own account
user.list.empty=No users found

# User Form Fields
user.field.fullName=Full Name
user.field.email=Email
user.field.password=Password
user.field.company=Company
user.field.phone=Phone
user.field.country=Country
user.field.language=Language
user.field.city=City
user.field.workFormat=Work Format
user.field.position=Position
user.field.joinedDate=Joined Date
user.field.enabled=Account Enabled
user.field.accountLocked=Account Locked
user.field.roles=Roles
user.field.status=Status
user.field.createdAt=Created At
user.field.updatedAt=Updated At
user.phone.country.code=BR (+55)
user.photo.allowed.files=Allowed files JPG, GIF or PNG. Max 800K

# User Status
user.status.active=Active
user.status.inactive=Inactive
user.status.locked=Locked
user.status.unlocked=Unlocked
user.status.online=Online
user.status.online=Online

# User Filters
user.filter.fullName=Filter by name...
user.filter.email=Filter by email...
user.filter.company=Filter by company...
user.filter.company.all=All Companies
user.filter.company.none=Without Company
user.filter.role.all=All Roles
user.filter.status.all=All Status
user.filter.lock.all=All Locks
user.filter.clear=Clear Filters

# User Statistics
user.stats.total=Total Users
user.stats.total.description=All registered users
user.stats.active=Active Users
user.stats.active.description=Enabled accounts
user.stats.inactive=Inactive Users
user.stats.inactive.description=Disabled accounts
user.stats.locked=Locked Users
user.stats.locked.description=Locked accounts
user.stats.without.company=Users Without Company
user.stats.without.company.description=Users without assigned company

# User Buttons
user.button.add=Add User
user.button.create=Create User
user.button.update=Update User
user.button.edit=Edit
user.button.delete=Delete
user.button.deactivate=Deactivate
user.button.activate=Activate
user.button.lock=Lock
user.button.unlock=Unlock
user.button.cancel=Cancel
user.button.save=Save
user.button.refresh=Refresh
user.button.new.photo=New photo
user.button.reset=Reset
user.button.save.changes=Save changes
user.photo.allowed.files=Allowed files JPG, GIF or PNG. Max 800K
user.phone.country.code=BR (+55)

# User Form Options
user.form.select.country=Select Country
user.form.select.language=Select Language
user.form.select.workFormat=Select Work Format
user.form.select.company=Select Company

# User Profile and Security
page.user.my.profile=My Profile
user.tab.profile=Profile
user.tab.projects=Projects
user.security.change.password=Change Password
user.security.current.password=Current Password
user.security.new.password=New Password
user.security.confirm.new.password=Confirm New Password
user.security.password.requirements=Password Requirements:
user.security.recent.activity=Recent Activity
user.validation.current.password.required=Please enter your current password.
user.validation.new.password.required=Please enter the new password according to the requirements.
user.validation.confirm.password.required=Please enter the password confirmation.

# User Profile
page.user.my.profile=My Profile
user.tab.profile=Profile
user.tab.projects=Projects

# User Pagination
user.pagination.showing=Showing
user.pagination.of=of
user.pagination.users=users
user.pagination.label=User pagination

# Modal Messages
modal.title.delete.success=Deleted Successfully
modal.title.delete.error=Delete Error
modal.close=Close

# Signup Page
page.signup.title=Jobify - Sign Up
page.signup.heading=Sign Up for Jobify
page.signup.confirmPassword=Confirm Password
page.signup.button=Sign Up
page.signup.hasAccount=Already have an account?
page.signup.loginLink=Login here

# Dashboard Page
page.dashboard.title=Dashboard
page.dashboard.heading=User Dashboard

# Test Page
page.test.title=Test Page
page.test.heading=Test Page

# Error Pages
error.403.title=Access Denied
error.403.heading=⚠️ Access Denied ⚠️
error.403.message=You don't have permission to access this page
error.403.button.home=Back to Home

# User Validation Messages
user.validation.company.required=Company is required
user.validation.roles.required=Please select a role (USER or MODERATOR)
user.validation.language.required=Language is required
user.validation.workFormat.required=Work format is required
user.validation.roles.exclusive=Please select only one role
user.password.requirements=Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.
user.password.validation.pattern=Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character (@#$!%*?&.;:<>)
user.password.validation.message=Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character

# Password Requirements Messages
validation.password.req.length=At least 8 characters
validation.password.req.lowercase=One lowercase letter (a-z)
validation.password.req.uppercase=One uppercase letter (A-Z)
validation.password.req.number=One number (0-9)
validation.password.req.special=One special character (@#$!%*?&.;:<>)

# Common Messages
common.close=Close

# Alert Messages
alert.error.title=Error
alert.warning.title=Warning
alert.info.title=Information
alert.success.title=Success
alert.error.unknown=An unknown error occurred
alert.error.user.exists=This user already exists
alert.error.email.exists=A user with this email address already exists
alert.error.server=A server error occurred while processing your request
alert.error.network=Unable to connect to the server. Please check your internet connection
alert.error.timeout=The request is taking longer than expected. Please try again
alert.error.validation=Please correct the validation errors and try again

# User Success Messages
user.created.message=User has been successfully created
user.updated.message=User has been successfully updated

# Field Validation Messages
user.field.fullName.required=Full name is required
user.field.fullName.minlength=Full name must be at least 2 characters long
user.field.fullName.maxlength=Full name cannot exceed 100 characters
user.field.email.required=Email address is required
user.field.email.invalid=Please enter a valid email address
user.field.password.required=Password is required
user.field.company.required=Company selection is required
user.field.language.required=Language selection is required
user.field.workFormat.required=Work format selection is required
user.field.phone.maxlength=Phone number cannot exceed 20 characters
user.field.city.maxlength=City name cannot exceed 100 characters
user.field.position.maxlength=Position cannot exceed 100 characters

# Authentication Error Messages
auth.error.user.disabled=Your account is disabled. Please contact support to activate your account.
auth.error.account.locked=Your account has been locked. Please contact support.
auth.error.credentials.expired=Your credentials have expired. Please reset your password.
auth.error.account.expired=Your account has expired. Please contact support.
auth.error.company.disabled=Your company account ({0}) has been disabled. Please contact support for assistance.
auth.error.company.locked=Your company account ({0}) has been temporarily locked. Please contact support for assistance.

# User Profile Page
page.user.profile.title=My Profile
user.tooltip.company=Company
user.tooltip.position=Position
user.tooltip.work.format=Work Format
user.tooltip.joined.date=Joined Date

# User Edit Form
user.field.email.label=E-mail
user.validation.email.required=Registration email is required.
user.field.phone.label=Phone
user.field.country.label=Country
user.form.country.placeholder=Choose country
user.field.language.label=Language
user.form.language.placeholder=Choose Language
user.field.city.label=City
user.field.work.format.label=Work Format
user.form.work.format.placeholder=Choose format
user.field.position.label=Position
user.field.joined.date.label=Joined Date

# Security Page
page.user.security.title=Security
user.security.change.password.title=Change Password
user.security.current.password.label=Current Password
user.security.new.password.label=New Password
user.security.confirm.new.password.label=Confirm New Password
user.security.password.requirements.title=Password Requirements:
user.security.recent.activity.title=Recent Activity
