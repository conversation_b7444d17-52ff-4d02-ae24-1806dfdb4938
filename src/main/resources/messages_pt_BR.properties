page.landing.menu.contact=Contato
page.title.user.details=Detalhes do Usuário


#Page Contents
page.login.bad.credentials.message=Usuário ou Senha inválidos!
page.user.content.password.changed.message=Senha Alterada!
page.content.generic.error=Algum erro aconteceu!
page.content.not.defined=--n/d--

#Buttons
page.user.button.change.password=Alterar Senha


#Messages
modal.title.save.success=Sucesso
modal.title.save.error=Erro
modal.text.save.success=As alterações foram gravadas!
modal.text.save.error=As alterações não foram gravadas!
page.landing.menu.home=Início
page.landing.menu.features=Funcionalidades
page.landing.menu.team=Equipe
page.landing.menu.faq=Perguntas
page.login.title=Bem-Vindo ao Jobify! 👋
general.language.lang_1=Português
general.language.lang_2=Inglês
page.landing.menu.login=Entrar
page.login.subtitle=Por favor entre com as suas credencias
page.login.password=Senha
page.login.email=Email
page.login.rememberme=Lembrar-me
page.login.forgotpassword=Esqueceu a senha?
page.login.requestaccess=Solicitar aqui
page.login.withoutaccess=Ainda não possui acesso?
page.user.security.change.password.req.01=A nova senha não pode conter espaço
page.user.security.change.password.req.02=A nova senha tem de ter pelo menos um caractere maísculo
page.user.security.change.password.req.03=A nova senha tem de ter pelo menos um caractere minúsculo
page.user.security.change.password.req.04=A nova senha tem de ter pelo menos um número de 0 a 9.
page.user.security.change.password.req.05=A nova senha tem de ter pelo menos um caractere especial
page.user.security.change.password.req.06=A nova senha tem de ter no mínimo 8 caracteres
page.user.security.change.password.req.07=A nova senha não deve conter sequências de 4 ou mais números
page.user.security.change.password.req.09=A senha digitada não coincide!
page.user.security.change.password.req.08=A nova passoword não pode ser igual à atual
modal.text.save.current.password.error=A senha atual não foi digitada corretamente
modal.text.save.password.matching=As palavra-passe não dão match!
page.user.my.account=Minha Conta
page.user.security=Segurança
page.login.captcha.verify=Por favor, verifique se não é um robô
page.login.token.expired=Sua sessão expirou. Por favor, faça login novamente.
page.navbar.profile=Meu Perfil
page.navbar.logout=Sair
page.navbar.my.account=Minha Conta
page.navbar.faqs=FAQs
page.user.security.password.policies.01=Mínimo de 8 caracteres - quanto mais, melhor
page.user.security.password.policies.02=Pelo menos um caractere minúsculo
page.user.security.password.policies.03=Pelo menos um número de 0 a 9
page.user.security.password.policies.04=Pelo menos uma letra maiúscula
page.user.security.password.policies.05=Pelo menos caractere especial sem ser o espaço (ex: @#$!%*?&.;:<>)
page.user.security.password.policies.06=Não deve conter sequências de 4 ou mais números
page.user.reset.password.input.password=Nova Senha
page.user.reset.password.input.confirmation=Confirmação de Nova Senha
page.user.reset.password.button.reset=Resetar Senha
page.user.reset.password.button.back=Voltar para Login
page.user.reset.password.input.password.hint=A senha deve cumprir os requisitos abaixo
page.user.reset.password.requirements=Requisitos da senha:
page.user.reset.password.title=Definir Nova Senha
page.user.reset.password.subtitle=Por favor, digite sua nova senha abaixo
page.user.reset.password.success.submessage=Agora você pode efetuar login com sua nova senha.
page.user.reset.password.success.title=Redefinição de senha bem-sucedida!
message.resetPassword.success=Sua senha foi redefinida com sucesso. Por favor, faça login.
message.resetPassword.emailNotFound=Nenhuma conta encontrada com esse endereço de e-mail.
message.resetPassword.linkSent=Se existir uma conta para {0}, um link de redefinição de senha foi enviado.
message.resetPassword.tokenInvalidOrExpired=Token de redefinição de senha inválido ou expirado.
message.resetPassword.formError=Por favor, corrija os erros no formulário.
message.resetPassword.passwordsDontMatch=As senhas não coincidem.
email.resetPassword.subject=Jobify - Redefinir senha
message.resetPassword.emailSendError=Não foi possível enviar o e-mail de redefinição de senha. Tente novamente mais tarde ou entre em contato com o suporte.
validation.password.notBlank=A senha não pode estar vazia.
validation.password.size=A senha deve ter pelo menos 8 caracteres.
validation.password.pattern=A senha deve conter pelo menos uma letra maiúscula, uma letra minúscula, um número e um caractere especial (@#$!%*?&.;:<>).
validation.confirmPassword.notBlank=A confirmação da senha não pode estar vazia.
error.tooManyRequests=Demasiadas solicitações. Tente novamente mais tarde.
page.user.forgot.password.button.back=Voltar ao login
page.user.forgot.password.button.send=Enviar link de redefinição
page.user.forgot.password.input.email=E-mail
page.user.forgot.password.subtitle=Digite seu e-mail e enviaremos instruções para redefinir sua senha
page.user.forgot.password.title=Esqueceu sua senha? 🔒
page.user.forgot.password.input.email.placeholder=Digite seu e-mail...
page.user.forgot.password.success.message=Verifique sua caixa de entrada de e-mail e pasta de spam para encontrar o link para redefinição de senha.
page.user.forgot.password.success.title=E-mail enviado com sucesso!
page.user.reset.password.error.title=Falha na redefinição da senha ❌
page.user.reset.password.error.submessage=Tente novamente ou solicite um novo link para redefinição de senha.
email.password.reset.footer.01=Obrigado,
email.password.reset.p.02=Você (ou alguém em seu nome) solicitou recentemente a redefinição da senha da sua conta Jobify. Se foi você, clique no botão abaixo para definir uma nova senha:
email.password.reset.p.01=Olá
email.password.reset.title=Solicitação de redefinição de senha
email.password.reset.p.03=Se você não solicitou a redefinição de senha, ignore este e-mail. Este link é válido por 60 minutos.
email.password.reset.button=Redefinir sua senha

# User Activation Email
email.user.activation.title=Complete Seu Registro de Usuário
email.user.activation.p.01=Olá
email.user.activation.p.02=Sua conta de usuário foi criada no Jobify. Para completar o processo de registro e definir sua senha, clique no botão abaixo.
email.user.activation.p.03=Este link é válido apenas pelas próximas 24 horas. Se você não esperava este e-mail, por favor ignore-o.
email.user.activation.footer.01=Obrigado,
email.user.activation.footer.02=A Equipe Jobify
email.user.activation.button=Completar Registro

# User Activation Messages
message.userActivation.tokenInvalidOrExpired=O link de ativação é inválido ou expirou. Entre em contato com o administrador.
message.userActivation.success=Sua conta foi ativada com sucesso! Agora você pode fazer login com seu e-mail e senha.

# User Activation Page
page.user.activate.title=Ativar Conta de Usuário
page.user.activate.success.title=Ativação de Conta Bem-Sucedida!
page.user.activate.success.message=Sua conta foi ativada com sucesso!
page.user.activate.success.submessage=Agora você pode fazer login com seu e-mail e senha.
page.user.activate.error.title=Falha na Ativação da Conta ❌
page.user.activate.error.submessage=Por favor, tente novamente ou entre em contato com o suporte para assistência.
page.user.activate.form.title=Complete Seu Registro
page.user.activate.form.subtitle=Defina sua senha para ativar sua conta
page.user.activate.form.password=Senha
page.user.activate.form.confirmPassword=Confirmar Senha
page.user.activate.form.button=Ativar Conta
page.user.activate.form.back=Voltar para Login

# Notifications Modal
modal.notifications.title=Todas as Notificações
modal.notifications.subtitle=Gerencie suas notificações
modal.notifications.status.new=Nova
modal.notifications.status.read=Lida
modal.notifications.status.archived=Arquivada
modal.notifications.empty.message=Nenhuma notificação encontrada
modal.notifications.close=Fechar
modal.notifications.mark.all.read=Marcar todas como lidas
modal.notifications.archive.all=Arquivar todas
modal.notifications.pagination.showing=Mostrando
modal.notifications.pagination.to=a
modal.notifications.pagination.of=de
modal.notifications.pagination.notifications=notificações
modal.notifications.pagination.page=Página
modal.notifications.pagination.previous=Anterior
modal.notifications.pagination.next=Próxima
modal.notifications.pagination.first=Primeira página
modal.notifications.pagination.last=Última página
modal.notifications.list.title=Lista das Minhas Notificações
dropdown.notifications.title=Notificações
dropdown.notifications.latest.indicator=(Últimas 10)
dropdown.notifications.new.singular=Nova
dropdown.notifications.new.plural=Novas

#Recent Activity Filters
filter.activity.all.events=Todos os eventos
filter.date.placeholder=DD/MM/AAAA
filter.date.title=Filtrar por data (ex: 25/12/2024, 25/12, 12/2024)
filter.clear.title=Limpar filtros

#Recent Activity Table
table.header.filters=Filtros
table.header.browser=Navegador
table.header.ip.address=Endereço IP
table.header.location=Localização
table.header.date.time=Data/Hora
table.header.event=Evento
table.browser.on=no
table.pagination.showing.from=Mostrando de
table.pagination.to=até
table.pagination.of.total=do total de
table.pagination.entries=entradas

# Company management
company.title=Gerenciamento de Empresas
company.list.title=Lista de Empresas
company.form.title.create=Criar Empresa
company.form.title.edit=Editar Empresa
company.form.description=Formulário para adicionar ou editar informações da empresa
company.field.id=ID
company.field.companyName=Nome da Empresa
company.field.address=Endereço
company.field.country=País
company.field.country.select=Selecionar País
company.field.country.placeholder=Pesquisar e selecionar país...
company.field.country.no.results=Nenhum país encontrado
company.field.country.searching=Pesquisando...
company.field.taxNumber=Número Fiscal
company.field.taxNumber.readonly=O número fiscal não pode ser alterado
company.field.domainName=Nome do Domínio
company.field.status=Status
company.field.lock=Status de Bloqueio
company.field.createdAt=Criado em
company.field.updatedAt=Atualizado em
company.button.create=Criar Empresa
company.button.edit=Editar
company.button.delete=Excluir
company.button.save=Salvar
company.button.cancel=Cancelar
company.button.clear.filters=Limpar Filtros
company.button.refresh=Atualizar
company.button.lock=Bloquear
company.button.unlock=Desbloquear
company.button.enable=Ativar
company.button.disable=Desativar
company.button.view.users=Ver Usuários
company.filter.companyName=Filtrar por Nome da Empresa
company.filter.country=Filtrar por País
company.filter.taxNumber=Filtrar por Número Fiscal
company.filter.country.all=Todos os Países
company.filter.status.all=Todos os Status
company.filter.lock.all=Todos os Bloqueios
company.validation.error=Por favor, corrija os erros de validação
company.create.success=Empresa criada com sucesso
company.create.error=Erro ao criar empresa
company.update.success=Empresa atualizada com sucesso
company.update.error=Erro ao atualizar empresa
company.delete.success=Empresa excluída com sucesso
company.delete.error=Erro ao excluir empresa
company.lock.success=Empresa bloqueada com sucesso
company.lock.error=Erro ao bloquear empresa
company.unlock.success=Empresa desbloqueada com sucesso
company.unlock.error=Erro ao desbloquear empresa
company.enable.success=Empresa ativada com sucesso
company.enable.error=Erro ao ativar empresa
company.disable.success=Empresa desativada com sucesso
company.disable.error=Erro ao desativar empresa
company.delete.confirm=Tem certeza de que deseja excluir esta empresa?
company.delete.confirm.title=Tem certeza?
company.delete.confirm.text=Deseja excluir a empresa: {0}?
company.delete.confirm.yes=Sim, excluir!
company.delete.confirm.cancel=Cancelar
company.lock.confirm=Tem certeza de que deseja bloquear esta empresa?
company.unlock.confirm=Tem certeza de que deseja desbloquear esta empresa?
company.enable.confirm=Tem certeza de que deseja ativar esta empresa?
company.disable.confirm=Tem certeza de que deseja desativar esta empresa?
company.not.found=Empresa não encontrada
company.tax.number.exists=Já existe uma empresa com o número fiscal {0}
company.tax.number.cannot.change=O número fiscal não pode ser alterado

# Company UI texts
company.loading=Carregando...
company.list.empty=Nenhuma empresa encontrada
company.field.taxNumber.help=Este deve ser único para cada empresa
company.field.domainName.help=Opcional - domínio do site da empresa
company.pagination.label=Paginação de empresas

# Company Status
company.status.enabled=Ativada
company.status.disabled=Desativada
company.status.locked=Bloqueada
company.status.unlocked=Desbloqueada

# Company Management
company.management.title=Gerenciamento de Empresas
company.management.subtitle=Gerencie empresas e suas informações

# Company Statistics
company.stats.total=Total de Empresas
company.stats.total.description=Todas as empresas registradas
company.stats.enabled=Empresas Ativadas
company.stats.enabled.description=Empresas ativas
company.stats.disabled=Empresas Desativadas
company.stats.disabled.description=Empresas inativas
company.stats.locked=Empresas Bloqueadas
company.stats.locked.description=Empresas temporariamente bloqueadas

# Company Users Management
company.users.title=Usuários da Empresa
company.users.description=Visualizar e gerenciar usuários desta empresa
company.users.search.placeholder=Pesquisar usuários...
company.users.clear.filter=Limpar filtro
company.users.no.users.found=Nenhum usuário encontrado para esta empresa
company.users.selected.count={0} usuários selecionados
company.users.selected.count.suffix=usuários selecionados
company.users.remove.from.company=Remover da Empresa
company.users.remove.confirm=Tem certeza de que deseja remover os usuários selecionados desta empresa?
company.users.add.title=Adicionar Usuários à Empresa
company.users.add.selected=Adicionar Selecionados
company.users.available.search.placeholder=Pesquisar usuários disponíveis...
company.users.available.no.users.found=Nenhum usuário disponível encontrado
company.users.pagination.label=Paginação de usuários

# Table headers
table.header.actions=Ações

# Table pagination
table.pagination.showing.from=Mostrando de
table.pagination.to=até
table.pagination.of.total=do total de
table.pagination.entries=registros
table.pagination.users=usuários

# Common UI elements
common.search.and.select=Pesquisar e selecionar...
common.loading=Carregando...
common.cancel=Cancelar
common.close=Fechar

# Menu items
menu.companies=Empresas
menu.users=Usuários
menu.admin=Administrador

# User Management
user.management.title=Gerenciamento de Usuários
user.management.subtitle=Gerenciar usuários do sistema, funções e permissões
user.list.title=Lista de Usuários
user.create.title=Criar Novo Usuário
user.edit.title=Editar Usuário
user.create.success=Usuário criado com sucesso
user.create.error=Erro ao criar usuário
user.update.success=Usuário atualizado com sucesso
user.update.error=Erro ao atualizar usuário
user.delete.success=Usuário excluído com sucesso
user.delete.error=Erro ao excluir usuário
user.delete.confirm=Tem certeza de que deseja excluir este usuário?
user.deactivate.success=Usuário desativado com sucesso
user.deactivate.error=Erro ao desativar usuário
user.deactivate.confirm=Tem certeza de que deseja desativar este usuário?
user.activate.success=Usuário ativado com sucesso
user.activate.error=Erro ao ativar usuário
user.activate.confirm=Tem certeza de que deseja ativar este usuário?
user.lock.success=Usuário bloqueado com sucesso
user.lock.error=Erro ao bloquear usuário
user.lock.confirm=Tem certeza de que deseja bloquear este usuário?
user.unlock.success=Usuário desbloqueado com sucesso
user.unlock.error=Erro ao desbloquear usuário
user.unlock.confirm=Tem certeza de que deseja desbloquear este usuário?
user.not.found=Usuário não encontrado
user.email.exists=Já existe um usuário com o email {0}
user.cannot.delete.self=Não é possível excluir sua própria conta
user.list.empty=Nenhum usuário encontrado

# User Form Fields
user.field.fullName=Nome Completo
user.field.email=Email
user.field.password=Senha
user.field.company=Empresa
user.field.phone=Telefone
user.field.country=País
user.field.language=Idioma
user.field.city=Cidade
user.field.workFormat=Formato de Trabalho
user.field.position=Cargo
user.field.joinedDate=Data de Ingresso
user.field.enabled=Conta Ativada
user.field.accountLocked=Conta Bloqueada
user.field.roles=Funções
user.field.status=Status
user.field.createdAt=Criado em
user.field.updatedAt=Atualizado em
user.phone.country.code=BR (+55)
user.photo.allowed.files=Arquivos permitidos JPG, GIF ou PNG. Máx de 800K

# User Status
user.status.active=Ativo
user.status.inactive=Inativo
user.status.locked=Bloqueado
user.status.unlocked=Desbloqueado
user.status.online=Online

# User Filters
user.filter.fullName=Filtrar por nome...
user.filter.email=Filtrar por email...
user.filter.company=Filtrar por empresa...
user.filter.company.all=Todas as Empresas
user.filter.company.none=Sem Empresa
user.filter.role.all=Todas as Funções
user.filter.status.all=Todos os Status
user.filter.lock.all=Todos os Bloqueios
user.filter.clear=Limpar Filtros

# User Statistics
user.stats.total=Total de Usuários
user.stats.total.description=Todos os usuários registrados
user.stats.active=Usuários Ativos
user.stats.active.description=Contas habilitadas
user.stats.inactive=Usuários Inativos
user.stats.inactive.description=Contas desabilitadas
user.stats.locked=Usuários Bloqueados
user.stats.locked.description=Contas bloqueadas
user.stats.without.company=Usuários Sem Empresa
user.stats.without.company.description=Usuários sem empresa atribuida

# User Buttons
user.button.add=Adicionar Usuário
user.button.create=Criar Usuário
user.button.update=Atualizar Usuário
user.button.edit=Editar
user.button.delete=Excluir
user.button.deactivate=Desativar
user.button.activate=Ativar
user.button.lock=Bloquear
user.button.unlock=Desbloquear
user.button.cancel=Cancelar
user.button.save=Salvar
user.button.refresh=Atualizar
user.button.new.photo=Nova foto
user.button.reset=Resetar
user.button.save.changes=Salvar alterações

# User Form Options
user.form.select.country=Selecionar País
user.form.select.language=Selecionar Idioma
user.form.select.workFormat=Selecionar Formato de Trabalho
user.form.select.company=Selecionar Empresa

# User Profile and Security
page.user.my.profile=Meu Perfil
user.tab.profile=Perfil
user.tab.projects=Projetos
user.security.change.password=Alterar Senha
user.security.current.password=Senha Atual
user.security.new.password=Nova Senha
user.security.confirm.new.password=Confirmar Nova Senha
user.security.password.requirements=Requisitos da Senha:
user.security.recent.activity=Atividade Recente
user.validation.current.password.required=Por favor digite a senha atual.
user.validation.new.password.required=Por favor digite a nova senha de acordo com os requisitos.
user.validation.confirm.password.required=Por favor digite a confirmação da senha.

# User Pagination
user.pagination.showing=Mostrando
user.pagination.of=de
user.pagination.users=usuários
user.pagination.label=Paginação de usuários

# Modal Messages
modal.title.delete.success=Excluído com Sucesso
modal.title.delete.error=Erro ao Excluir
modal.close=Fechar

# Signup Page
page.signup.title=Jobify - Cadastro
page.signup.heading=Cadastre-se no Jobify
page.signup.confirmPassword=Confirmar Senha
page.signup.button=Cadastrar
page.signup.hasAccount=Já possui uma conta?
page.signup.loginLink=Faça login aqui

# Dashboard Page
page.dashboard.title=Painel
page.dashboard.heading=Painel do Usuário

# Test Page
page.test.title=Página de Teste
page.test.heading=Página de Teste

# Error Pages
error.403.title=Acesso Negado
error.403.heading=⚠️ Acesso Negado ⚠️
error.403.message=Você não tem permissão para acessar esta página
error.403.button.home=Voltar para o Início

# User Validation Messages
user.validation.company.required=Empresa é obrigatória
user.validation.roles.required=Por favor, selecione um papel (USUÁRIO ou MODERADOR)
user.validation.language.required=Idioma é obrigatório
user.validation.workFormat.required=Formato de trabalho é obrigatório
user.validation.roles.exclusive=Por favor, selecione apenas um papel

# Password Requirements Messages
validation.password.req.length=Pelo menos 8 caracteres
validation.password.req.lowercase=Uma letra minúscula (a-z)
validation.password.req.uppercase=Uma letra maiúscula (A-Z)
validation.password.req.number=Um número (0-9)
validation.password.req.special=Um caractere especial (@#$!%*?&.;:<>)

# Common Messages
common.close=Fechar

# Alert Messages
alert.error.title=Erro
alert.warning.title=Aviso
alert.info.title=Informação
alert.success.title=Sucesso
alert.error.unknown=Ocorreu um erro desconhecido
alert.error.user.exists=Este usuário já existe
alert.error.email.exists=Já existe um usuário com este endereço de email
alert.error.server=Ocorreu um erro no servidor ao processar sua solicitação
alert.error.network=Não foi possível conectar ao servidor. Verifique sua conexão com a internet
alert.error.timeout=A solicitação está demorando mais do que o esperado. Tente novamente
alert.error.validation=Corrija os erros de validação e tente novamente

# User Success Messages
user.created.message=Usuário foi criado com sucesso
user.updated.message=Usuário foi atualizado com sucesso

# Authentication Error Messages
auth.error.user.disabled=Sua conta está desabilitada. Entre em contato com o suporte para ativar sua conta.
auth.error.account.locked=Sua conta foi bloqueada. Entre em contato com o suporte.
auth.error.credentials.expired=Suas credenciais expiraram. Por favor, redefina sua senha.
auth.error.account.expired=Sua conta expirou. Entre em contato com o suporte.
auth.error.company.disabled=A conta da sua empresa ({0}) foi desabilitada. Entre em contato com o suporte para assistência.
auth.error.company.locked=A conta da sua empresa ({0}) foi temporariamente bloqueada. Entre em contato com o suporte para assistência.

# User Profile Page
page.user.profile.title=Meu Perfil
user.tooltip.company=Empresa
user.tooltip.position=Função
user.tooltip.work.format=Formato de Trabalho
user.tooltip.joined.date=Data de Início
user.status.online=Online

# User Edit Form
user.button.new.photo=Nova foto
user.button.reset=Reset
user.photo.allowed.files=Arquivos permitidos JPG, GIF ou PNG. Máx de 800K
user.field.email.label=E-mail
user.validation.email.required=Email de cadastro obrigatório.
user.field.phone.label=Celular
user.field.country.label=País
user.form.country.placeholder=Escolha o país
user.field.language.label=Língua
user.form.language.placeholder=Escolha a Língua
user.field.city.label=Cidade
user.field.work.format.label=Formato de Trabalho
user.form.work.format.placeholder=Escolha o formato
user.field.position.label=Função
user.field.joined.date.label=Data de Início
user.button.cancel=Cancelar

# Security Page
page.user.security.title=Segurança
user.security.change.password.title=Alterar Senha
user.security.current.password.label=Senha Atual
user.security.new.password.label=Nova Senha
user.security.confirm.new.password.label=Confirmar Nova Senha
user.security.password.requirements.title=Requisitos da Senha:
user.security.recent.activity.title=Atividade Recente

# App Names
app.name.purchasing=Compras
app.name.finance=Finanças
app.name.budgets=Orçamentos
