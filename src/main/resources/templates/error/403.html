<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout"
      layout:decorate="~{layout}">

<head>
    <title>Access Denied - 403</title>
</head>

<body>

<div layout:fragment="content">
    <div class="col-12">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <!-- Error Icon -->
                        <div class="mb-4">
                            <i class="ti ti-shield-x text-danger" style="font-size: 4rem;"></i>
                        </div>

                        <!-- Error Title -->
                        <h1 class="display-4 text-danger mb-3">403</h1>
                        <h2 class="h4 mb-3">Access Denied</h2>

                        <!-- Error Message -->
                        <div class="alert alert-danger" role="alert">
                            <h5 class="alert-heading">
                                <i class="ti ti-exclamation-triangle me-2"></i>
                                Insufficient Permissions
                            </h5>
                            <p class="mb-0">
                                You don't have permission to access this application or resource. 
                                This could be because:
                            </p>
                            <ul class="list-unstyled mt-3 mb-0 text-start">
                                <li><i class="ti ti-point me-2"></i>Your company doesn't have access to this application</li>
                                <li><i class="ti ti-point me-2"></i>You haven't been granted access to this application</li>
                                <li><i class="ti ti-point me-2"></i>Your role level is insufficient for this operation</li>
                                <li><i class="ti ti-point me-2"></i>Your account may be disabled or locked</li>
                            </ul>
                        </div>

                        <!-- Help Information -->
                        <div class="card bg-light border-0 mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ti ti-info-circle me-2"></i>
                                    Need Access?
                                </h6>
                                <p class="card-text small mb-0">
                                    Contact your system administrator or company admin to request access to this application.
                                    Make sure to specify which application you need access to and your business justification.
                                </p>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex flex-column flex-sm-row gap-2 justify-content-center">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-2"></i>
                                Go Back
                            </a>
                            <a th:href="@{/dashboard}" class="btn btn-primary">
                                <i class="ti ti-home me-2"></i>
                                Return to Dashboard
                            </a>
                        </div>

                        <!-- User Information (if available) -->
                        <div th:if="${currentUser}" class="mt-4 pt-3 border-top">
                            <small class="text-muted">
                                Logged in as: <strong th:text="${currentUser.fullName}">User Name</strong>
                                (<span th:text="${currentUser.email}"><EMAIL></span>)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
