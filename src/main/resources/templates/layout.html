<!DOCTYPE html>

<html th:lang="${#locale.language}" class="light-style layout-compact layout-navbar-fixed layout-menu-fixed"
      dir="ltr"
      data-theme="theme-default"
      data-assets-path="/assets/"
      data-template="vertical-menu-template"
      data-style="light"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout">

<head th:replace="~{fragments/header :: header}">
    <title layout:title-pattern="$CONTENT_TITLE - $LAYOUT_TITLE">Jobify</title>
</head>

<body>

<!-- Layout wrapper -->
<div class="layout-wrapper layout-content-navbar  ">
    <div class="layout-container">

        <div th:replace="${menuData}"></div>

        <!-- Layout container -->
        <div class="layout-page">

            <div th:replace="~{fragments/navbar :: navbar}"></div>

            <!-- Content wrapper -->
            <div class="content-wrapper">

                <!-- Content -->
                <div class="container-xxl flex-grow-1 container-p-y">
                    <!-- Layout Demo -->
                    <div class="row">

                        <!--begin::Thymeleaf-->
                        <div layout:fragment="content">

                        </div>
                        <!--end::Thymeleaf-->
                    </div>
                    <!--/ Layout Demo -->
                </div>
                <!-- / Content -->

            </div>
            <!-- Content wrapper -->

            <div th:replace="~{fragments/footer :: footer}"></div>

        </div>
        <!-- / Layout page -->
    </div>

    <!-- Overlay -->
    <div class="layout-overlay layout-menu-toggle"></div>

    <!-- Drag Target Area To SlideIn Menu On Small Screens -->
    <div class="drag-target"></div>

</div>
<!-- / Layout wrapper -->

<!-- All Notifications Modal -->
<div class="modal fade" id="allNotificationsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-simple">
        <div class="modal-content" style="height: 80vh; max-height: 750px; padding: 2rem;">
            <!-- Fixed Header -->
            <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- Fixed Title Section with Action Buttons -->
            <div class="px-4 pb-3">
                <div class="modal-notifications-header border-bottom mb-3 pb-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="text-start">
                            <h4 class="mb-1" th:text="#{modal.notifications.title}">All Notifications</h4>
                            <p class="text-body mb-0 small" th:text="#{modal.notifications.subtitle}">Manage your notifications</p>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <!-- Loading spinner in stable location -->
                            <div class="spinner-container position-relative">
                                <img id="modalLoadingSpinner" class="htmx-indicator" src="/img/bars.svg" alt="Loading..." style="width: 20px; height: 20px;"/>
                            </div>
                            <!-- Replaceable action buttons -->
                            <div th:replace="~{fragments/notifications-modal :: headerActionButtons}"></div>
                        </div>
                    </div>
                </div>
                <!-- Page Info -->
                <span class="text-muted small" th:if="${notificationPage != null}">
                    [[#{modal.notifications.pagination.page}]] <span th:text="${notificationPage.currentPage + 1}">1</span>
                    [[#{modal.notifications.pagination.of}]] <span th:text="${notificationPage.totalPages}">5</span>
                </span>
            </div>

            <!-- Scrollable Body -->
            <div class="modal-body pt-0" style="flex: 1; overflow-y: auto; max-height: calc(80vh - 200px);">
                <!-- Modal Content will be loaded here via HTMX -->
                <div id="notificationsModalContent">
                    <!-- Initial empty state -->
                    <div class="text-center py-5 text-muted">
                        <i class="ti ti-bell ti-48px mb-3"></i>
                        <p>Click to load notifications</p>
                    </div>
                </div>
            </div>

            <!-- Fixed Footer with Pagination -->
            <div class="modal-footer border-0 pt-4 pb-4" style="min-height: 80px;">
                <!-- Pagination Controls -->
                <div id="paginationControlsContainer" class="w-100 mb-3 px-4 py-2">
                    <div th:replace="~{fragments/notifications-modal :: paginationControls}"></div>
                </div>

                <!-- Close Button -->
                <div class="w-100 d-flex justify-content-center">
                    <button type="button" class="btn btn-outline-secondary px-4 py-2" data-bs-dismiss="modal" th:text="#{modal.notifications.close}">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- / All Notifications Modal -->

<!-- Core JS -->
<th:block th:replace="~{fragments/common-scripts :: core-js}"></th:block>

<!-- Optional Vendors JS -->
<th:block layout:fragment="optionalVendorJS"/>

<!-- Main JS -->
<th:block th:replace="~{fragments/common-scripts :: main-js}"></th:block>

<!-- Optional Page JS -->
<script th:src="@{/js/htmx-2.0.2.js}"></script>
<script src="https://unpkg.com/htmx-ext-multi-swap@2.0.0/multi-swap.js"></script>

<!-- Global JavaScript Functions -->
<script>
    // Global HTMX error handling - Let HTMX handle spinner automatically
    document.addEventListener('htmx:responseError', function(event) {
        console.error('HTMX request failed:', event.detail.xhr.status, event.detail.xhr.statusText);

        // Check for JWT expiration header
        const jwtExpired = event.detail.xhr.getResponseHeader('X-JWT-Expired');
        if (jwtExpired === 'true') {
            console.log('JWT token expired, triggering automatic logout');
            // Trigger HTMX logout endpoint
            htmx.ajax('GET', '/htmx-logout', {
                swap: 'none'
            });
            return;
        }

        // Prevent the default error handling
        event.detail.shouldSwap = false;
        event.detail.isError = false;
    });

    // Check for JWT expiration on every HTMX response
    document.addEventListener('htmx:afterRequest', function(event) {
        const jwtExpired = event.detail.xhr.getResponseHeader('X-JWT-Expired');
        if (jwtExpired === 'true') {
            console.log('JWT token expired detected in response, triggering automatic logout');
            // Trigger HTMX logout endpoint
            htmx.ajax('GET', '/htmx-logout', {
                swap: 'none'
            });
        }
    });

    document.addEventListener('htmx:timeout', function(event) {
        console.error('HTMX request timed out');
        // Prevent the default error handling
        event.detail.shouldSwap = false;
        event.detail.isError = false;
    });

    document.addEventListener('htmx:sendError', function(event) {
        console.error('HTMX send error:', event.detail.error);
        // Prevent the default error handling
        event.detail.shouldSwap = false;
        event.detail.isError = false;
    });

    function closeNotificationsDropdown() {
        try {
            // Method 1: Try jQuery if available (most reliable)
            if (typeof $ !== 'undefined') {
                $('#notificationsDropdownToggle').dropdown('hide');
                return;
            }

            // Method 2: Use Bootstrap 5 API
            var dropdownToggle = document.getElementById('notificationsDropdownToggle');
            if (dropdownToggle) {
                var dropdown = bootstrap.Dropdown.getInstance(dropdownToggle);
                if (dropdown) {
                    dropdown.hide();
                    return;
                }

                // Method 3: Create new instance and hide
                try {
                    var newDropdown = new bootstrap.Dropdown(dropdownToggle);
                    newDropdown.hide();
                    return;
                } catch (e) {
                    // Continue to manual method
                }
            }

            // Method 4: Manual DOM manipulation
            var dropdownMenu = document.querySelector('.dropdown-notifications .dropdown-menu');
            var parentDropdown = document.querySelector('.dropdown-notifications');

            if (dropdownMenu && dropdownMenu.classList.contains('show')) {
                dropdownMenu.classList.remove('show');
            }
            if (dropdownToggle) {
                dropdownToggle.classList.remove('show');
                dropdownToggle.setAttribute('aria-expanded', 'false');
            }
            if (parentDropdown && parentDropdown.classList.contains('show')) {
                parentDropdown.classList.remove('show');
            }

            // Method 5: Force close with timeout
            setTimeout(function() {
                var allDropdowns = document.querySelectorAll('.dropdown-menu.show');
                allDropdowns.forEach(function(dropdown) {
                    if (dropdown.closest('.dropdown-notifications')) {
                        dropdown.classList.remove('show');
                    }
                });

                var allDropdownToggles = document.querySelectorAll('.dropdown-toggle[aria-expanded="true"]');
                allDropdownToggles.forEach(function(toggle) {
                    if (toggle.closest('.dropdown-notifications')) {
                        toggle.setAttribute('aria-expanded', 'false');
                        toggle.classList.remove('show');
                    }
                });
            }, 100);

        } catch (error) {
            console.log('Error closing dropdown:', error);
        }
    }
</script>

<th:block layout:fragment="optionalPageJS"/>

<!-- End Of Layout -->
</body>
</html>
