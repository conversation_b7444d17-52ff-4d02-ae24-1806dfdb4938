<!DOCTYPE html>

<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head>
    <title>Header</title>
</head>

<body>

<!-- Menu -->
<aside th:fragment="menu" id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">

    <div class="app-brand demo ">
        <a th:href="@{/user/}" class="app-brand-link">
            <span class="app-brand-logo demo">
                <img th:src="@{/assets/img/logo/jobify-icon.svg}" alt="Jobify Logo">
            </span>
            <span class="app-brand-text demo menu-text fw-bold">Jobify</span>
        </a>
        <a id="layout-menu-toggle" href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
            <i class="ti menu-toggle-icon d-none d-xl-block align-middle"></i>
            <i class="ti ti-x d-block d-xl-none ti-md align-middle"></i>
        </a>
    </div>

    <div class="menu-inner-shadow"></div>
    <ul class="menu-inner py-1">
        <!-- Apps & Pages -->
        <li class="menu-header small">
            <span class="menu-header-text" data-i18n="Apps & Pages">Apps &amp; Pages</span>
        </li>
        <li class="menu-item">
            <a th:href="@{/user/}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-dashboard"></i>
                <div data-i18n="Dashboard">Dashboard</div>
            </a>
        </li>
        <li class="menu-item">
            <a th:href="@{/user/teste}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-user"></i>
                <div data-i18n="Teste">Teste</div>
            </a>
        </li>

        <!-- User Available Apps -->
        <th:block th:if="${userAvailableApps != null and userAvailableApps.size() > 0}">
            <li class="menu-header small">
                <span class="menu-header-text" th:text="#{menu.apps}">Applications</span>
            </li>
            <th:block th:each="app : ${userAvailableApps}">
                <li th:if="${app != null and app.appName != null and app.appName != ''}"
                    th:class="${'menu-item' + (currentAppName != null and currentAppName == app.appName.toLowerCase() ? ' active' : '')}">
                    <a th:href="@{'/app/' + ${app.appName != null ? app.appName.toLowerCase() : ''}}"
                       th:class="${'menu-link' + (currentAppName != null and currentAppName == app.appName.toLowerCase() ? ' active' : '')}">
                        <i class="menu-icon tf-icons"
                           th:class="${'menu-icon tf-icons ' +
                                     (app.appName != null && app.appName.toLowerCase() == 'purchasing' ? 'ti ti-shopping-cart' :
                                     (app.appName != null && app.appName.toLowerCase() == 'finance' ? 'ti ti-currency-dollar' :
                                     (app.appName != null && app.appName.toLowerCase() == 'budgets' ? 'ti ti-calculator' : 'ti ti-apps')))}"></i>
                        <div th:text="${app.appName != null ? #messages.msg('app.name.' + app.appName.toLowerCase()) : 'Unknown App'}">App Name</div>
                        <!-- Active indicator -->
                        <span th:if="${currentAppName != null and currentAppName == app.appName.toLowerCase()}"
                              class="badge badge-center rounded-pill bg-primary ms-auto">
                            <i class="ti ti-point ti-xs"></i>
                        </span>
                    </a>
                </li>
            </th:block>
        </th:block>

        <!-- Admin Only Menu Items -->
        <li class="menu-header small" th:if="${mainRole == 'Admin'}" sec:authorize="hasRole('ADMIN')">
            <span class="menu-header-text" th:text="#{menu.admin}">Administrator</span>
        </li>
        <li class="menu-item" th:if="${mainRole == 'Admin'}" sec:authorize="hasRole('ADMIN')">
            <a th:href="@{/admin/companies}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-building"></i>
                <div th:text="#{menu.companies}">Companies</div>
            </a>
        </li>
        <li class="menu-item" th:if="${mainRole == 'Admin'}" sec:authorize="hasRole('ADMIN')">
            <a th:href="@{/admin/users}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-users"></i>
                <div th:text="#{menu.users}">Users</div>
            </a>
        </li>
    </ul>
</aside>
<!-- / Menu -->

</body>

</html>
