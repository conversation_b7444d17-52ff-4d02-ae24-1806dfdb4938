<!DOCTYPE html>

<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head>
    <title>Header</title>
</head>

<body>

<!-- Menu -->
<aside th:fragment="menu" id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">

    <div class="app-brand demo ">
        <a th:href="@{/user/}" class="app-brand-link">
            <span class="app-brand-logo demo">
                <img th:src="@{/assets/img/logo/jobify-icon.svg}" alt="Jobify Logo">
            </span>
            <span class="app-brand-text demo menu-text fw-bold">Jobify</span>
        </a>
        <a id="layout-menu-toggle" href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
            <i class="ti menu-toggle-icon d-none d-xl-block align-middle"></i>
            <i class="ti ti-x d-block d-xl-none ti-md align-middle"></i>
        </a>
    </div>

    <div class="menu-inner-shadow"></div>
    <ul class="menu-inner py-1">
        <!-- Apps & Pages -->
        <li class="menu-header small">
            <span class="menu-header-text" data-i18n="Apps & Pages">Apps &amp; Pages</span>
        </li>
        <li class="menu-item">
            <a th:href="@{/user/}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-dashboard"></i>
                <div data-i18n="Dashboard">Dashboard</div>
            </a>
        </li>
        <li class="menu-item">
            <a th:href="@{/user/teste}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-user"></i>
                <div data-i18n="Teste">Teste</div>
            </a>
        </li>

        <!-- User Available Apps -->
        <th:block th:if="${userAvailableApps != null && !userAvailableApps.isEmpty()}">
            <li class="menu-header small">
                <span class="menu-header-text" th:text="#{menu.apps}">Applications</span>
            </li>
            <li class="menu-item" th:each="app : ${userAvailableApps}" th:if="${app != null && app.appName != null}">
                <a th:href="@{'/app/' + ${#strings.toLowerCase(app.appName)}}" class="menu-link">
                    <i class="menu-icon tf-icons"
                       th:classappend="${#strings.toLowerCase(app.appName) == 'purchasing'} ? 'ti ti-shopping-cart' :
                                       (${#strings.toLowerCase(app.appName) == 'finance'} ? 'ti ti-currency-dollar' :
                                       (${#strings.toLowerCase(app.appName) == 'budgets'} ? 'ti ti-calculator' : 'ti ti-apps'))"></i>
                    <div th:text="#{${'app.name.' + #strings.toLowerCase(app.appName)}}">App Name</div>
                </a>
            </li>
        </th:block>

        <!-- Admin Only Menu Items -->
        <li class="menu-header small" th:if="${mainRole == 'Admin'}" sec:authorize="hasRole('ADMIN')">
            <span class="menu-header-text" th:text="#{menu.admin}">Administrator</span>
        </li>
        <li class="menu-item" th:if="${mainRole == 'Admin'}" sec:authorize="hasRole('ADMIN')">
            <a th:href="@{/admin/companies}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-building"></i>
                <div th:text="#{menu.companies}">Companies</div>
            </a>
        </li>
        <li class="menu-item" th:if="${mainRole == 'Admin'}" sec:authorize="hasRole('ADMIN')">
            <a th:href="@{/admin/users}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-users"></i>
                <div th:text="#{menu.users}">Users</div>
            </a>
        </li>
    </ul>
</aside>
<!-- / Menu -->

</body>

</html>